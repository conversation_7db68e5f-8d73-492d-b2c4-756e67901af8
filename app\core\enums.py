import re
from enum import Enum
from typing import NamedTuple, Optional, List, Dict

# Chart Types 
class ChartType(Enum):
    SYMBOL_MAP = "SymbolMap"
    FILLED_MAP = "filledMap"
    LINE = "Line"
    AREA = "Area"
    PIE = "Pie"
    HIGHLIGHTED_TABLE = "Square"
    PIVOT_TABLE = "PivotTable"
    BAR = "Bar"
    HORIZONTAL_BAR = "HorizontalBar"
    VERTICAL_BAR = "VerticalBar"
    STACKED_VERTICAL_BAR = "StackedVerticalBar"
    STACKED_HORIZONTAL_BAR = "StackedHorizontalBar"
    TEXT_TABLE = "TextTable"
    TREE_MAP = "TreeMap"
    SLICER = "Slicer"
    CARDS = "Cards"
    TEXT_BOX = "TextBox"
    BAR_AND_AREA = "Bar & Area"
    BAR_AND_LINE = "Bar & Line"
    SCATTER = "ScatterPlot"
    MIXED_CHART = "MixedChart"
    AUTOMATIC = "Automatic"
    MULTIPOLYGON = "Multipolygon"
    DEFAULT_DASHBOARD_NAME = "UNTITLED"
    DONUT_CHART = "Donut"
    BUBBLE_CHART ="Bubble_chart"
    CIRCLE = "Circle"
    WATER_FALL = "WaterFallChart"
    GANTT_BAR = "Gantt_bar"
    SQUARE = "Square"
    AREA_AND_LINE = "Area & Line"
    LINE_AND_AREA = "Line & Area"
    BULLET_CHART = "Bullet_Chart"
    POLYGON_WITH_TABLE = "Polygon_with_table"
    POLYGON = "Polygon"
#File Status
class FileStatus(Enum):
    SUCCESS = "SUCCESS"
    FAILURE = "Failure"
    ERROR = "ERROR"
#Server Status 
class ServerStatus(Enum):
    ACTIVE = "ACTIVE"
    INACTIVE = "INACTIVE"

class ComplexityTypeEnum(str, Enum):
    SIMPLE = "Simple"
    MEDIUM = "Medium"
    COMPLEX = "Complex"
    HIGHLY_COMPLEX = "Highly complex"

class DiscoverSiteStatus(Enum):
    INITIATED = "Initiated"
    STARTED = "Started"
    IN_PROGRESS = "InProgress"
    COMPLETED = "Completed"
    FAILED = "Failed"


#  Server Authentication 
class ServerAuthType(str, Enum):
    CREDENTIALS = "CREDENTIALS"
    PAT = "PAT"

class RoleEnum(str, Enum):
    ADMIN = "Admin"
    MANAGER = "Manager"
    DEVELOPER = "Developer"

#  Datasource Types 
class Datasource_types(Enum):
    MSSQL = "mssql"
    POSTGRESQL = "postgresql"
    MYSQL = "mysql"
    SQLITE = "sqlite"
    ORACLE = "oracle"
    ALLOWED_TYPES = ["mssql", "postgresql", "mysql", "sqlite", "oracle"]


# Server Type 
class ServerType(str, Enum):
    ONPREMISE = "ONPREMISE"
    CLOUD = "CLOUD"


# Important Keys
class ImportantKeys(Enum):
    PASSWORD = "password"
    KEYS_TO_CHANGE_DTYPES_TO_STR = [
        "id", "_sa_instance_state", "created_at", "updated_at"
    ]


# Dashboard Constants
class Dashboard(Enum):
    DASHBOARD = ".//dashboard"
    NAME = "name"
    DS_DEPENDENCIES = ".//datasource-dependencies"
    ZONE = ".//zone"
    COLUMN = ".//column"
    STYLE_FORMAT = ".//style-rule/format"
    ZONE_STYLE = ".//zone-style"
    VIEW = ".//view"
    ENCODINGS = ".//encodings/*"


# Datasource Constants
class Datasource(Enum):
    DSS = "datasources"
    Datasources = "Datasources"
    DS = ".//datasource"
    DATASOURCES = ".//datasources"
    COLUMN = "column"
    CALC = "calculation"
    CALC_IN_DS = "Calculations"
    FORMULA = "formula"
    NAME = "name"
    CAPTION = "caption"
    FILTERS = "Filters"
    NAMED_CONNECTIONS = "./connection/named-connections"
    RELATION = ".//relation"
    COLS_MAP = ".//cols/map"
    CONNECTION = "./connection"
    META_RECORD = ".//metadata-record"
    RELATIONSHIP = ".//relationship"
    FIRST_END_POINT = "./first-end-point"
    SECOND_ENC_POINT = "./second-end-point"
    EXPRESSION = "./expression"
    FILTER = ".//filter"
    COLUMNS_COLUMN = "columns/column"
    EXTRACT = ".//extract"
    METADATA_RECORD_CLASS = ".//metadata-record[@class='column']"
    REMOTE_NAME = "remote-name"
    LOCAL_TYPE = "local-type"
    PARENT_NAME = "parent-name"
    AGGREGATION = "aggregation"
    OBJECT_ID = "object-id"
    OBJECT_TAG = ".//object"
    ID = "id"
    CONNECTION_CLASS = "class"
    CONNECTION_DIRECTORY = "directory"

# General Keys 
class GeneralKeys(Enum):
    NAME = "name"
    PATH = "path"
    COLUMN = "column"
    TYPE = "type"
    ROLE = "role"
    DATATYPE = "datatype"
    CLASS = "class"
    SIZE = "size"
    Datasources = "datasources"
    Datasource = "datasource"
    DS_DEPENDENTIES = "datasource-dependencies"
    ZONES = "zones"
    CALC = "calculation"
    FORMULA = "formula"
    CAPTION = "caption"
    WorkSheets ="Worksheets"
    CONNECTION = "connection"
    NAMED_CONNECTION = "named-connection"
    FILE_NAME = "filename"
    LOCAL_NAME = "local-name"
    LOCAL_TYPE = "local-type"
    GROUP_FILTER = "groupfilter"
    PARAMETERS = "Parameters"
    VALUE = "value"
    KEY = "key"
    OBJECT_ID = "object-id"
    EXPRESSION = "expression"
    NULL = "NULL"
    OP = "op"
    TABLE = "table"
    DB_NAME = "dbname"
    COLUMN_INSTANCES = "column-instances"
    COLUMN_INSTANCE = "column-instance"
    FILTERS = "filters"
    SLICES = "slices"
    ATTR = "attr"
    MARK = "mark"
    ENCODINGS = "encodings"
    STYLES = "styles"
    STYLE = "style"
    ROWS = "rows"
    COLS = "cols"
    PANES = "panes"
    PARAM = "param"
    TYPE2 = "type-v2"
    VIEW = "view"
    COLUMNS = "columns"
    LAYOUT = "layout"
    UNTITLED = "Untitled Dashboard"
    DATASOURCE_COUNT = "datasource_count"
    STATUS = "status"
    TXT_FILE = ".txt"
    CHART_TYPE = "chart_type"
    CHART_TYPES = "chart_types"
    ERROR = "error"
    DATE_TIME_SET = {'date', 'datetime'}
    QUANTITATIVE = "quantitative"
    TWB_FILE_EXTENSION = ".twb"
    NO_COLUMNS_AVAILABLE = "No columns available"
    UNKNOWN = "unknown"
    COLUMN_NAME_KEY = "column_name"
    MAPPING_VALUE_KEY = "mapping_value"
    TABLE_COLUMN_SEPARATOR = "].["
    TABLE_COLUMN_PATTERN = re.compile(r'\[(.*?)\]\.\[([^\]]*)\]')
    UNNAMED_TABLE = "Unnamed Table"
    UNNAMED_COLUMN = "Unnamed Column"
    FONT_NAME = 'Arial'
    DEFAULT_FONT_SIZE_PT = 11
    DOC_TITLE = "Summary"
    TABLE_HEADERS_VISUALS = ['Report Name', 'Visual Name', 'Visual Type']
    MIGRATION_AUTOMATIC = "Biport"
    MIGRATION_MANUAL = "Manual"
    DASHBOARDS = "Dashboards"
    UNTITLED_DASHBOARDS = "untitled dashboards"
    Dashboards = "Dashboards"
    Columns_and_Datatypes = "Columns and Data Types"
    DEFAULT_Schema = "Default Schema"
    CONNECTION_DETAILS = "connection_details"
    UNKNOWN_TYPE = "Unknown Type"
    CALCULATIONS = "Calculations"
    DASHBOARD_JSON = "dashboard.json"
    SUPPORTED_FILE_EXTENSION = ".txt"
    TOOLTIP = 'tooltip'
    QK = 'qk'
    NK = 'nk'
    REFLINE = "refline"
    QUANTATIVE = "quantitative"
    DEFAULT_DASHBOARD_NAME = "UNTITLED"
    UNNMAED_COLUMN = "Unnamed column"
    CONNECTION_TYPE = "connection_type"
    HYPER = "hyper"
    EXTRACT = "Extract"
    LIVE = "Live"
    EXTRACT_CLASS = "extract_class"
    UNKNOWN_DATASOURCE = "unknown datasource"
    HYPER_FILE = "Hyper file"
    EXCEL = "Excel"
    CSV_OR_TEXT_FILE = 'CSV or Text File'
    CALCULATION_ = "Calculation_"
    NOT_INCLUDING_WHILE_FETCHING_DIMENTIONS = ["mode", "param" ]
    Z_DEFAULT_ZERO = "0.00"
    LAYOUTS = "layouts"
    ID = "id"
    POSITION = "position"
    X = "x"
    Y = "y"
    H = "h"
    W = "w"
    MEASURE = "measure"
    TEXT_BOX_VISUAL = "text_box_visual"

class FilterValues(Enum):
    YR = "yr"
    QR = "qr"
    MN = "mn"
    WK = "wk"
    DY = "dy"
    TYR = "tyr"
    TQR = "tqr"
    TMN = "tmn"
    TWK = "twk"
    TDY = "tdy"
    YEAR = "Year"
    MONTH = "Month"
    QUARTER = "Quarter"
    COUNT = "Count"
    AVERAGE = "Average"
    DAY = "Day"
    SUM = "sum"
    CNT = "cnt"
    USR = "usr"
    AVG = "avg"
    MAX = "max"


# Table Constants
class TableConstants:
    COLUMNS_KEY = "Columns and Data Types"
    TYPE_KEY = "Type"
    TABLE_TYPE_DEFAULT = "table"


# Worksheet Constants 
class WorkSheet(Enum):
    PANES_PANE = ".//panes/pane"
    PANE = ".//pane"
    PANES = ".//panes"
    ENCODINGS = ".//encodings"
    ENCODING_COLOR = ".//encodings/color"
    ENCODING_TEXT = ".//encodings/text"
    PANES_PATH_ATT = ".//encodings/path"
    ALL_ENCODINGS = ".//panes/pane//encodings/*"
    ENCODINGS_IN_ALL = ".//encodings/*"
    ROWS = ".//rows"
    COLS = ".//cols"
    DS_COL_INSTANCES = ".//datasource-dependencies/column-instance"
    DS_COLS = ".//datasource-dependencies/column"
    NAME = "name"
    COLUMN = "column"
    TYPE = "type"
    ROLE = "role"
    DATATYPE = "datatype"
    COLUMN_INSTANCE = ".//column-instance"
    MARK = ".//mark"
    CLASS = "class"
    MAP_SOURCES = ".//mapsources"
    LONGITUDE = "Longitude"
    LATITUDE = "Latitude"
    SIZE = "size"
    AUTOMATIC = "Automatic"
    WORKSHEETS = ".//worksheet"
    COLS_MAP = ".//cols/map"
    FILTER = ".//filter"
    SLICES_COLUMN = ".//slices/column"
    STYLE_RULE_FORMAT = ".//style-rule/format"
    STYLE_RULE = ".//style-rule"
    REFERENCE_LINE = ".//reference-line"


# Complexity Levels
class ComplexityLevel(Enum):
    SIMPLE = "Simple"
    MEDIUM = "Medium"
    COMPLEX = "Complex"
    HIGHLY_COMPLEX = "Highly complex"


class TableauXMLTags(Enum):
    LOD = "lod"
    AXIS = "axis"
    NAME = "name"
    TEXT = "text"
    VIEW = "view"
    ATTR = "attr"
    FIELD = "field"
    SCOPE = "scope"
    CLASS = "class"
    TITLE = "title"
    TABLE = "table"
    VALUE = "value"
    COLOR = "color"
    FORMAT = "format"
    COLUMN = "column"
    ELEMENT = "element"
    TOOLTIP = "tooltip"
    ENCODING = "encoding"
    STYLE_RULE = "style-rule"
    DATASOURCE_NAME = "datasource"
    LOCAL_TYPE = "local-type"
    LOCAL_NAME = "local-name"
    PARENT_NAME = "parent-name"
    REMOTE_NAME = "remote-name"
    CALCULATION = "calculation"
    AGGREGATION = "aggregation"
    COMPUTED_SORT = "computed-sort"
    LAYOUT_OPTIONS = "layout-options"
    FORMATTED_TEXT = "formatted-text"
    SIZE = "size"
    USING = "@using"
    DIRECTION = "@direction"
    ROWS = ".//rows"
    COLS = ".//cols"
    PANE = ".//pane"
    MARK = ".//mark"
    STYLE = ".//style"
    ZONES = ".//zones"
    ZONE = ".//zone"
    FILTER = ".//filter"
    WORKSHEET = ".//worksheet"
    ENCODINGS = ".//encodings"
    DASHBOARD = ".//dashboard"
    DATASOURCE = ".//datasource"
    DATASOURCE_DEPENDENCIES = ".//datasource-dependencies"
    TITLE_RUN = ".//layout-options/title/formatted-text/run"
    METADATA_RECORDS = ".//metadata-record[@class='column']"
    WINDOW_ELEMENTS = "./windows/window"
    DASHBOARD_TITLE = "./layout-options/title/formatted-text/run"
    WORKSHEETS_DIMENSIONS_WITH_NAMES = "worksheets_with_zones"
    TAB_ORDER = "tabOrder"
    CELL = "cell"
    NORMAL = "normal"
    NONE = "none"
    FONT_STYLE = "font-style"
    FONT_WEIGHT = "font-weight"
    FONT_SIZE = "font-size"
    FONT_FAMILY = "font-family"
    FONT_COLOR = "font-color"
    TEXT_DECORATION = "text-decoration"
    LABEL = "label"
    FORMATTED_TEXT_RUN = "formatted-text/run"
    FORMAT_TAG = ".//format"
    STYLE_RULES = ".//table/style/style-rule"
    TEXT_BOX_TEXT = ".//table/view/datasource-dependencies/column/calculation"
    MARK_TEXT = "mark"
    PALETTE = "palette"
    COLOR_PALETTE = "color-palette"



class PowerBIReportKeys(Enum):
    NAME = "name"
    LAYOUTS = "layouts"
    SINGLE_VISUAL = "singleVisual"
    ACTIVE = "active"
    QUERYREF = "queryRef"
    CONFIG = "config"
    DISPLAY_NAME = "displayName"
    DISLAY_OPTION = "displayOption"
    ORDINAL = "ordinal"
    X = "X"
    Y = "Y"
    Y2 = "Y2"
    ROWS = "Rows"
    COLUMNS = "Columns"
    SIZE = "Size"
    VALUES = "Values"
    GROUP = "Group"
    SERIES = "Series"
    CATEGORY = "Category"
    TOOLTIPS = "Tooltips"
    VISUAL_TYPE = "visualType"
    PROJECTIONS = "projections"
    VERSION = "Version"
    FROM = "From"
    SELECT = "Select"
    PROTOTYPE_QUERY = "prototypeQuery"
    DRILL_FILTER_OTHER_VISUAL = "drillFilterOtherVisuals"
    HAS_DEFAULT_SORT = "hasDefaultSort"
    ENTITY = "Entity"
    TYPE = "Type"
    NAME_IN_FROM_LIST = "Name"
    SUM = "Sum"
    MAX = "Max"
    VISUAL_CONTAINERS = "visualContainers"
    FILTERS = "filters"
    HEIGHT = "height"
    WIDTH = "width"
    EMPTY_DICTIONARY = "{}"
    EMPTY_SQUARE = "[]"
    DIMENSIONS = {"x":"x","y":"y","z":"z","h":"height","w":"width"}


    OBJECTS = "objects"
    COLOR = "color"
    FONT_FAMILY = "fontFamily"


class PowerBISemanticModelKeys(Enum):
    LINEAGE_TAG = "lineageTag"

class PowerBITemplateKeys(Enum):
    VISUAL_TYPE = "visual_type"
    PROJECTIONS_DATA = "projections_data"
    TABLES_LIST = "tables_list"
    TABLE_NAME = "table_name"
    COLUMN = "column"
    FILTER = "filter"
    WORKSHEET_NAME = "worksheet_name"
    DIMENSIONS = "dimensions"
    TABLE_COLUMNS = "table_columns"
    COLUMN_VALUE = "column_value"
    UNIQUE_ID = "unique_id"
    COLUMN_DATA = "column_data"
    DATA_TYPE = "data_type"
    AGGREGATION = "aggregation"
    COLUMN_NAME = "column_name"
    FORMAT_STRING = "format_string"
    SUMMARIZE_BY = "summarize_by"
    SOURCE_COLUMN = "source_column"
    LINEAGE_TAG = "lineage_tag"
    TEXT_BOX = "textBox"
    OBJECTS = "objects"
    VARIATION = "variation"
    LOCAL_DATE_TABLE_REF = "local_date_table_ref"
    RELATIONSHIP_ID = "relationship_id"
    FORMULA = "formula"
    CONNECTION_TYPE = "connection_type"
    DIRECTORY = "directory"
    SERVER = "server"
    DATABASE = "database"    

class PowerBIChartTypes(Enum):
    LINE_CHART = "lineChart"
    AREA = "areaChart"
    CLUSTERED_COLUMN = "clusteredColumnChart"
    CLUSTERED_BAR = "clusteredBarChart"
    PIVOT_TABLE = "pivotTable"
    LINE_STACKED_COLUMN_COMBO_CHART = "lineStackedColumnComboChart"
    FILLED_MAP = "filledMap"
    MAP = "map"
    PIE = "pieChart"
    SCATTER = "scatterChart"
    COLUMN_CHART = "columnChart"
    BAR_CHART = "barChart"
    TABLE_EX = "tableEx"
    TREE_MAP = "treemap"
    CUSTOM = "custom"
    TEXT_BOX = "textbox"


class VisualRequest(NamedTuple):
    worksheet_name: str 
    visual_type: str
    rows: Optional[List] = []
    cols: Optional[List] = []
    panes: Optional[List] = []
    filters: Optional[List] = []
    table_column_data: List = []
    datasource_columns: Optional[List] = []
    worksheet_style_data: Optional[Dict] = {}
    worksheet_title_data: Optional[List] = {}
    calculations_related_data: Optional[Dict] = {}
    small_multiples: bool = False

class PowerBIObjectKeys(Enum):
    LINE_STYLES = "lineStyles"
    LEGEND = "legend"
    LABELS = "labels"
    CATEGORY_AXIS = "categoryAxis"
    VALUE_AXIS = "valueAxis"
    Y2_AXIS = "y2Axis"
    QUICK_FILTER = "quickFilter"
    SHOW = "show"
    DATA_POINT = "dataPoint"
    SWITCH_AXIS_POSITION = "switchAxisPosition"
    LABEL_PRECISION = "labelPrecision"
    SHOW_AXIS_TITLE = "showAxisTitle"
    TITLE_COLOR = "titleColor"
    TITLE_FONT_SIZE = "titleFontSize"
    TITLE_FONT_STYLE = "titleFontStyle"
    TITLE_FONT_WEIGHT = "titleFontWeight"
    TITLE_TEXT_DECORATION = "titleTextDecoration"
    LABEL_COLOR = "labelColor"
    TITLE_ITALIC = "titleItalic"
    TITLE_BOLD = "titleBold"
    TITLE_UNDERLINE = "titleUnderline"
    UNDERLINE = "underline"
    BOLD = "bold"
    ITALIC = "italic"
    FONT_SIZE = "fontSize"
    TRUE = "true"
    FALSE = "false"
    COLOR = "color"
    FONT_FAMILY = "fontFamily"
    SHOW_MARKER = "showMarker"
    SHOW_LEGEND = "showLegend"
    POSITION = "position"
    VALUE = "value"
    RIGHT = "right"
    LEFT = "left"
    CENTER = "center"
    FONT_ALIGNMENT = "fontalignment"
    PT = "pt"
    ONE = "1"
    TWO = "2"
    EMPTY = ""
    BG_COLOR = "background-color"
    FONT_STYLE = "font-style"
    TEXT_DECORATION = "text-decoration"
    FONT_WEIGHT = "font-weight"
    HEADER = "header"
    LABEL = "label"
    PURE_WHITE = "#ffffff"
    MARKERS = "markers"
    TRANSPARENCY = "transparency"
    ENTITY = "Entity"
    PROPERTY = "Property"
    FILTER_VALUE = "filterValue"
    MIN = "min"
    MID = "mid"
    MAX = "max"
