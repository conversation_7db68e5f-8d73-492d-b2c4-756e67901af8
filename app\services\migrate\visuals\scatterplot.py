from app.services.migrate.Tableau_Analyzer.report.core import generate_projections_data
from ..core import *
from app.core import scatter_json
import uuid, json
from app.core.logger_setup import logger
from app.core.enums import (
    ChartType, PowerBITemplateKeys, TableauXMLTags,
    PowerBIReportKeys, PowerBIChartTypes, VisualRequest, PowerBIObjectKeys
)
from app.services.migrate.Tableau_Analyzer.report import (
    remove_duplicate_fields,
    extract_multiple_encodings_data
)
from app.services.migrate.Tableau_Analyzer.report.core import (
    extract_datasource_filter_column, 
    find_table_name, 
    fetch_label_data, 
    fetch_format_data,
    extract_color_encoding_from_dict
)

def check_color_field_role(color_field, table_column_data, datasource_column_list):
    if color_field:
        field_filter, field_column, field_table_name = get_calc_filter_column(color_field, table_column_data, datasource_column_list)
        for column_data in datasource_column_list:
            column_name = column_data.get("@name")
            column_name = column_name.strip("[]")
            if column_name == field_column:
                field_role = column_data.get("@role")
                if field_role == "dimension":
                    return True
                return False
            
def process_scatter_colors(color_field, panes, style, table_column_data, datasource_column_list):
    if color_field:
        values_input, value_queryref, min_color, mid_color, max_color = get_color_formatting(style, color_field, table_column_data, datasource_column_list)
        return {"properties":{"fill": {"solid": {"color": {"expr": {"FillRule": {"Input": values_input,"FillRule": {"linearGradient3": {"min": {"color": {"Literal": {"Value": f"'{min_color}'"}}},"mid": {"color": {"Literal": {"Value": f"'{mid_color}'"}}},"max": {"color": {"Literal": {"Value": f"'{max_color}'"}}},"nullColoringStrategy": {"strategy": {"Literal": {"Value": "'asZero'"}}}}}}}}}}},"selector": {"data": [{"dataViewWildcard": {"matchingOption": 1}}]}}
    if panes:
        panes_data = to_list(panes)
        style_rule_data = panes_data[0].get("style",{}).get("style-rule")
        if style_rule_data:
            style_rule_data = to_list(style_rule_data)
            for data in style_rule_data:
                if data.get("@element") == "mark":
                    format_data = data.get("format")
                    format_data = to_list(format_data)
                    for item in format_data:
                        if item.get("@attr") == "mark-color":
                            color = item.get("@value")
                            return {"properties":{"fill":{"solid":{"color":create_expr(f"'{color}'")}}}}

    return {"properties":{"fill":{"solid":{"color":create_expr("'#4e79a7'")}}}}

def get_scatter_objects_data(color_field, panes, style, table_column_data, datasource_column_list):
    objects = {}
    datapoint_details = process_scatter_colors(color_field, panes, style, table_column_data, datasource_column_list)
    objects["dataPoint"] = [datapoint_details]
    objects["legend"] = [{"properties": {"show": create_expr("false")}}]
    objects["markers"] = [{"properties": {"transparency": create_expr("100D")}}]
    return objects

def fetch_scatter_objects(color_field, style, table_column_data, rows, cols):
    objects = {}

    datasource_id, column_value, filter_value = extract_datasource_filter_column(color_field[0])
    label_data = fetch_label_data(style)
    if label_data:
        objects[PowerBIObjectKeys.LABELS.value] = label_data
    
    if style:
        rows_category_axis = fetch_format_data(style, rows, "rows")
        cols_value_axis = fetch_format_data(style, cols, "cols")

        if rows and rows_category_axis:
            objects[PowerBIObjectKeys.VALUE_AXIS.value] = rows_category_axis
        if cols and cols_value_axis:
            objects[PowerBIObjectKeys.CATEGORY_AXIS.value] = cols_value_axis

    table_name = find_table_name(
                table_columns=table_column_data,
                datasource_name=datasource_id,
                column_name=column_value
            )
    result = extract_color_encoding_from_dict(style)
    if result:
        if TableauXMLTags.COLOR_PALETTE.value in result:
            colors = result.get(TableauXMLTags.COLOR_PALETTE.value, {}).get(TableauXMLTags.COLOR.value)
            if colors:
                min_color, mid_color, max_color = colors[0], colors[len(colors)//2], colors[(len(colors)-1)]
        if TableauXMLTags.PALETTE.value in result:
            palette_name = result.get(TableauXMLTags.PALETTE.value)
            if palette_name:
                color_data = COLOR_PALETTE_DATA.get(palette_name)
                min_color, mid_color, max_color = color_data.get("Min"), color_data.get("Mid"), color_data.get("Max")
    
    objects[PowerBIObjectKeys.DATA_POINT.value] = [{
        PowerBIObjectKeys.ENTITY.value: table_name,
        PowerBIObjectKeys.PROPERTY.value: column_value,
        PowerBIObjectKeys.FILTER_VALUE.value: filter_value,
        PowerBIObjectKeys.MIN.value: min_color,
        PowerBIObjectKeys.MID.value: mid_color,
        PowerBIObjectKeys.MAX.value: max_color
    }]
    return objects

def get_scatter_plot(rows,cols,pane_encodings,table_column_data,datasource_column_list,worksheet_name, worksheet_title_layout, style):
    try:
        scatter_result, projection_data = [], {}
        color_data, tooltip_data, lod_data, text_data = process_pane_encodings(pane_encodings)

        tooltips_data = process_tooltip_data(
            data = [tooltip_data, lod_data, text_data], 
            visual_fields= [rows, cols])

        final_values_data = None
        if lod_data:
            lod_coloumn_data = [data.get("@column") for data in lod_data]
            final_values_data = process_multiple_fields(lod_coloumn_data)

        if not lod_data and text_data:
            text_column_data = [data.get("@column") for data in text_data]
            final_values_data = process_multiple_fields(text_column_data)

        result_queryref,tables_list,select_list = get_projections_data([rows, cols, final_values_data, tooltips_data], table_column_data, datasource_column_list)

        x_queryref = result_queryref.get(cols)
        y_queryref = result_queryref.get(rows)
        category_queryref = result_queryref.get(final_values_data)

        if x_queryref: projection_data["X"] = x_queryref
        if y_queryref: projection_data["Y"] = y_queryref
        if category_queryref: projection_data["Category"] = category_queryref


        objects_data = get_scatter_objects_data(color_data, pane_encodings, style, table_column_data, datasource_column_list)

        from_list=get_from_list(tables_list)
        title_list = get_title_config(worksheet_title_layout, style)
        background_list = get_background_config(style= style)
        scatterplot_json={
            "visual_config_name" : f'{str(uuid.uuid4()).replace("-","")[:20]}',
            "select_list":json.dumps(select_list),
            "from_list":json.dumps(from_list),
            "projection_data":json.dumps(projection_data),
            "objects_data" : json.dumps(objects_data),
            "title_list" : json.dumps(title_list),
            "background_list" : json.dumps(background_list),
            "border_list" : get_border_config()
        }
        scatter_result.append({"config":scatterplot_json,"template":scatter_json})
        return scatter_result
    
    except Exception as e:
        logger.error(f"---Error in generating scatter plot visual for {worksheet_name}--- {str(e)}")
        raise ValueError(f"Error in generating scatter plot visual for {worksheet_name} - {str(e)}")


def process_scatter_chart_report(request: VisualRequest):
    """
    Processes the scatter chart report from the given request.
    Args:
        request (VisualRequest): The request object containing the necessary data.
    Returns:
        dict: A dictionary containing the processed scatter chart data.
    -----------
    """
    scatter_chart_result = {}
    rows = request.rows
    cols = request.cols
    panes = request.panes
    table_column_data = request.table_column_data
    calculations_related_data = request.calculations_related_data

    encodings = extract_multiple_encodings_data(
        panes=panes,
        tags_to_extract=[
            TableauXMLTags.TOOLTIP.value,
            TableauXMLTags.LOD.value,
            TableauXMLTags.TEXT.value,
            TableauXMLTags.COLOR.value
        ]
    )
    unique_color_data = remove_duplicate_fields(encodings[TableauXMLTags.COLOR.value])
    unique_lod_data = remove_duplicate_fields(encodings[TableauXMLTags.LOD.value])
    unique_text_data = remove_duplicate_fields(encodings[TableauXMLTags.TEXT.value])

    scatter_chart_fields_mapping = {
        PowerBIReportKeys.Y.value: rows,
        PowerBIReportKeys.X.value: cols,
        PowerBIReportKeys.CATEGORY.value: unique_lod_data or unique_text_data
    }
    projections_data = generate_projections_data(table_column_data, calculations_related_data, scatter_chart_fields_mapping)
    objects = fetch_scatter_objects(
        color_field=unique_color_data,
        style=request.worksheet_style_data,
        table_column_data=table_column_data,
        rows=rows,
        cols=cols,
    )
    scatter_chart_result[PowerBITemplateKeys.VISUAL_TYPE.value] = PowerBIChartTypes.SCATTER.value
    scatter_chart_result[PowerBITemplateKeys.WORKSHEET_NAME.value] = request.worksheet_name
    scatter_chart_result[PowerBITemplateKeys.PROJECTIONS_DATA.value] = projections_data
    scatter_chart_result[PowerBITemplateKeys.OBJECTS.value] = objects
    return scatter_chart_result
