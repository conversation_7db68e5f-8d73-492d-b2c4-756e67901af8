from app.core import *
from app.services.migrate.Tableau_Analyzer.report import (
    generate_projections_data, extract_multiple_encodings_data,
    remove_duplicate_fields
)
from app.core.enums import (ChartType, VisualRequest,
    TableauXMLTags, PowerBITemplateKeys, PowerBIObjectKeys,
    PowerBIReportKeys, PowerBIChartTypes
)
from app.services.migrate.Tableau_Analyzer.report.core import (
    extract_datasource_filter_column, 
    find_table_name, 
    fetch_label_data, 
    fetch_format_data,
    extract_color_encoding_from_dict
)

def fetch_bar_objects(color_field, style, table_column_data, rows, cols):
    objects = {}

    datasource_id, column_value, filter_value = extract_datasource_filter_column(color_field[0])
    label_data = fetch_label_data(style)
    if label_data:
        objects[PowerBIObjectKeys.LABELS.value] = label_data
    
    if style:
        rows_category_axis = fetch_format_data(style, rows, "rows")
        cols_value_axis = fetch_format_data(style, cols, "cols")

        if rows and rows_category_axis:
            objects[PowerBIObjectKeys.VALUE_AXIS.value] = cols_value_axis
        if cols and cols_value_axis:
            objects[PowerBIObjectKeys.CATEGORY_AXIS.value] = rows_category_axis

    table_name = find_table_name(
                table_columns=table_column_data,
                datasource_name=datasource_id,
                column_name=column_value
            )
    result = extract_color_encoding_from_dict(style)
    if result:
        if TableauXMLTags.COLOR_PALETTE.value in result:
            colors = result.get(TableauXMLTags.COLOR_PALETTE.value, {}).get(TableauXMLTags.COLOR.value)
            if colors:
                min_color, mid_color, max_color = colors[0], colors[len(colors)//2], colors[(len(colors)-1)]
        if TableauXMLTags.PALETTE.value in result:
            palette_name = result.get(TableauXMLTags.PALETTE.value)
            if palette_name:
                color_data = COLOR_PALETTE_DATA.get(palette_name)
                min_color, mid_color, max_color = color_data.get("Min"), color_data.get("Mid"), color_data.get("Max")
    
    objects[PowerBIObjectKeys.DATA_POINT.value] = [{
        PowerBIObjectKeys.ENTITY.value: table_name,
        PowerBIObjectKeys.PROPERTY.value: column_value,
        PowerBIObjectKeys.FILTER_VALUE.value: filter_value,
        PowerBIObjectKeys.MIN.value: min_color,
        PowerBIObjectKeys.MID.value: mid_color,
        PowerBIObjectKeys.MAX.value: max_color
    }]
    return objects


def process_bar_chart(request: VisualRequest):
    barchart_result = {}
    panes = request.panes
    rows = request.rows
    cols = request.cols
    calculations_related_data = request.calculations_related_data
    table_column_data = request.table_column_data
    encodings = extract_multiple_encodings_data(
        panes=panes,
        tags_to_extract=[
            TableauXMLTags.TOOLTIP.value,
            TableauXMLTags.LOD.value,
            TableauXMLTags.TEXT.value,
            TableauXMLTags.COLOR.value
        ]
    )
    unique_tooltips = remove_duplicate_fields(
        encodings[TableauXMLTags.TOOLTIP.value],
        encodings[TableauXMLTags.LOD.value],
        encodings[TableauXMLTags.TEXT.value],
        rows,
        cols
    )
    bar_chart_field_mappings = {
        PowerBIReportKeys.Y.value: (
            cols 
            if (request.visual_type in [ChartType.STACKED_HORIZONTAL_BAR.value, ChartType.HORIZONTAL_BAR.value])
            else rows
        ),
        PowerBIReportKeys.CATEGORY.value: (
            rows
            if (request.visual_type in [ChartType.STACKED_HORIZONTAL_BAR.value, ChartType.HORIZONTAL_BAR.value])
            else cols
        ),
    }
    if unique_tooltips: bar_chart_field_mappings[PowerBIReportKeys.TOOLTIPS.value] = unique_tooltips

    projections_data = generate_projections_data(table_column_data, calculations_related_data, bar_chart_field_mappings)
    objects = fetch_bar_objects(
            color_field=encodings[PowerBIObjectKeys.COLOR.value],
            style=request.worksheet_style_data,
            table_column_data=table_column_data,
            rows=rows,
            cols=cols,
        )

    barchart_result[PowerBITemplateKeys.VISUAL_TYPE.value] = (
        PowerBIChartTypes.CLUSTERED_BAR.value 
        if request.visual_type == ChartType.HORIZONTAL_BAR.value else PowerBIChartTypes.CLUSTERED_COLUMN.value 
        if request.visual_type == ChartType.VERTICAL_BAR.value else PowerBIChartTypes.CLUSTERED_BAR.value
        if request.visual_type == ChartType.HORIZONTAL_BAR.value else PowerBIChartTypes.CLUSTERED_COLUMN.value
    )
    barchart_result[PowerBITemplateKeys.WORKSHEET_NAME.value] = request.worksheet_name
    barchart_result[PowerBITemplateKeys.PROJECTIONS_DATA.value] = projections_data
    barchart_result[PowerBITemplateKeys.OBJECTS.value] = objects

    return barchart_result
